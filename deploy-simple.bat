@echo off
REM Simple deployment script for Windows Command Prompt
REM Usage: deploy-simple.bat [ResourceGroupName] [AppServiceName]

if "%1"=="" (
    echo Please provide Resource Group Name and App Service Name
    echo Usage: deploy-simple.bat [ResourceGroupName] [AppServiceName]
    echo Example: deploy-simple.bat "rg-musicapp" "musicapp-service"
    pause
    exit /b 1
)

if "%2"=="" (
    echo Please provide App Service Name
    echo Usage: deploy-simple.bat [ResourceGroupName] [AppServiceName]
    echo Example: deploy-simple.bat "rg-musicapp" "musicapp-service"
    pause
    exit /b 1
)

set RESOURCE_GROUP=%1
set APP_SERVICE=%2
set APP_URL=https://%APP_SERVICE%.azurewebsites.net

echo ========================================
echo Deploying Shining C Music App
echo Resource Group: %RESOURCE_GROUP%
echo App Service: %APP_SERVICE%
echo Target URL: %APP_URL%
echo ========================================

REM Update Blazor configuration
echo Updating Blazor configuration...
echo {> ShiningCMusicApp\wwwroot\appsettings.json
echo   "SyncfusionLicense": "MzkxNzI4MkAzMjM5MmUzMDJlMzAzYjMyMzkzYmlCSHVjUFNHMVcwWDI0Mm5reFc2M21MbXA4cEFVcWRRWXl1eFFUWnlXYTA9",>> ShiningCMusicApp\wwwroot\appsettings.json
echo   "ApiBaseUrl": "%APP_URL%/api">> ShiningCMusicApp\wwwroot\appsettings.json
echo }>> ShiningCMusicApp\wwwroot\appsettings.json

REM Clean and build
echo Cleaning previous builds...
dotnet clean

echo Restoring packages...
dotnet restore

echo Building solution...
dotnet build --configuration Release

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

REM Publish
echo Publishing application...
if exist publish rmdir /s /q publish
dotnet publish ShiningCMusicApi\ShiningCMusicApi.csproj --configuration Release --output publish

if errorlevel 1 (
    echo Publish failed!
    pause
    exit /b 1
)

REM Create zip
echo Creating deployment package...
if exist publish.zip del publish.zip
powershell -Command "Compress-Archive -Path 'publish\*' -DestinationPath 'publish.zip'"

REM Configure App Service
echo Configuring App Service settings...
az webapp config appsettings set --resource-group %RESOURCE_GROUP% --name %APP_SERVICE% --settings "ConnectionStrings__MusicSchool=Server=tcp:shining.database.windows.net,1433;Initial Catalog=MusicSchool;Persist Security Info=False;User ID=sam;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" "ApiBaseUrl=%APP_URL%/" "ASPNETCORE_ENVIRONMENT=Production"

REM Deploy
echo Deploying to Azure...
az webapp deployment source config-zip --resource-group %RESOURCE_GROUP% --name %APP_SERVICE% --src publish.zip

if errorlevel 1 (
    echo Deployment failed!
    pause
    exit /b 1
)

REM Clean up
echo Cleaning up...
rmdir /s /q publish
del publish.zip

REM Restart app service
echo Restarting App Service...
az webapp restart --resource-group %RESOURCE_GROUP% --name %APP_SERVICE%

echo ========================================
echo Deployment completed successfully!
echo Your application is available at: %APP_URL%
echo API endpoints: %APP_URL%/api
echo ========================================

pause
