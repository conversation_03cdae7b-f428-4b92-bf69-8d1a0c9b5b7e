using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Tutor
    {
        public int TutorId { get; set; }
        
        [StringLength(50)]
        public string? TutorName { get; set; }
        
        [StringLength(250)]
        public string? Email { get; set; }
        
        public DateTime CreatedUTC { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedUTC { get; set; }
        
        public bool IsArchived { get; set; } = false;
        
        [StringLength(20)]
        public string? LoginName { get; set; }

        [StringLength(7)]
        public string? Color { get; set; } = "#6C757D"; // Default gray color

        // Navigation properties
        public virtual ICollection<Student> Students { get; set; } = new List<Student>();
        public virtual ICollection<Lesson> Lessons { get; set; } = new List<Lesson>();
        public virtual User? User { get; set; }
    }
}
