using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface IUserService
    {
        Task<IEnumerable<User>> GetUsersAsync();
        Task<User?> GetUserAsync(string loginName);
        Task<User> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(string loginName, User user);
        Task<bool> DeleteUserAsync(string loginName);
        Task<User?> AuthenticateAsync(string loginName, string password);
        Task<IEnumerable<UserRole>> GetUserRolesAsync();
    }
}
