namespace ShiningCMusicCommon.Models
{
    public class ScheduleEvent
    {
        public int Id { get; set; }
        public string Subject { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public bool IsAllDay { get; set; } = false;
        public string? RecurrenceRule { get; set; }
        public int? RecurrenceID { get; set; }
        public string? RecurrenceException { get; set; }

        // Additional properties for lesson management
        public int TutorId { get; set; }
        public int StudentId { get; set; }
        public int SubjectId { get; set; }
        public int LocationId { get; set; }
        public string? TutorName { get; set; }
        public string? StudentName { get; set; }
        public string? SubjectName { get; set; }
        public string? LocationName { get; set; }
        public string? CategoryColor { get; set; }
        
        // Convert from Lesson to ScheduleEvent
        public static ScheduleEvent FromLesson(Lesson lesson)
        {
            return new ScheduleEvent
            {
                Id = lesson.LessonId,
                Subject = lesson.Student?.StudentName ?? "",
                StartTime = lesson.StartTime,
                EndTime = lesson.EndTime,
                Description = lesson.Description,
                Location = lesson.Location?.LocationName ?? "",
                RecurrenceRule = lesson.RecurrenceRule,
                TutorId = lesson.TutorId,
                StudentId = lesson.StudentId,
                SubjectId = lesson.SubjectId,
                LocationId = lesson.LocationId,
                TutorName = lesson.Tutor?.TutorName,
                StudentName = lesson.Student?.StudentName,
                SubjectName = lesson.Subject?.SubjectName,
                LocationName = lesson.Location?.LocationName
            };
        }

        // Convert from ScheduleEvent to Lesson
        public Lesson ToLesson()
        {
            return new Lesson
            {
                LessonId = Id,
                SubjectId = SubjectId,
                StartTime = StartTime,
                EndTime = EndTime,
                Description = Description,
                LocationId = LocationId,
                RecurrenceRule = RecurrenceRule,
                TutorId = TutorId,
                StudentId = StudentId,
                IsRecurring = !string.IsNullOrEmpty(RecurrenceRule)
            };
        }
    }
}
