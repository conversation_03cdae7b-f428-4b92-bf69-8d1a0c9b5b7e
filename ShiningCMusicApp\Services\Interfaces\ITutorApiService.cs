using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface ITutorApiService
    {
        Task<List<Tutor>> GetTutorsAsync();
        Task<Tutor?> GetTutorAsync(int id);
        Task<Tutor?> CreateTutorAsync(<PERSON><PERSON> tutor);
        Task<bool> UpdateTutorAsync(int id, Tu<PERSON> tutor);
        Task<bool> DeleteTutorAsync(int id);
        Task<bool> UpdateTutorColorAsync(int tutorId, string color);
    }
}
