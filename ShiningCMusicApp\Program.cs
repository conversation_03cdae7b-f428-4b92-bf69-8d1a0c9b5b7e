using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using ShiningCMusicApp;
using ShiningCMusicApp.Services;
using Syncfusion.Blazor;
using System.Net.Http.Json;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Load configuration from server endpoint (which can read environment variables)
var http = new HttpClient() { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) };
string apiBaseUrl = "https://localhost:7268/api"; // fallback

try
{
    // Try to get configuration from server endpoint first
    var serverConfig = await http.GetFromJsonAsync<ShiningCMusicCommon.Models.AppConfiguration>("api/configuration");

    if (serverConfig != null && !string.IsNullOrEmpty(serverConfig.ApiBaseUrl))
    {
        apiBaseUrl = serverConfig.ApiBaseUrl;

        // Register Syncfusion license from server configuration
        if (!string.IsNullOrEmpty(serverConfig.SyncfusionLicense))
        {
            Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(serverConfig.SyncfusionLicense);
        }

        Console.WriteLine($"Loaded configuration from server: {apiBaseUrl}");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"Failed to load server configuration: {ex.Message}");

    // Fallback to local appsettings.json
    try
    {
        var localConfig = await http.GetFromJsonAsync<Dictionary<string, string>>("appsettings.json");

        if (localConfig != null)
        {
            apiBaseUrl = localConfig.GetValueOrDefault("ApiBaseUrl", "https://localhost:7268/api");

            // Register Syncfusion license from local configuration
            if (localConfig.TryGetValue("SyncfusionLicense", out var licenseKey))
            {
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(licenseKey);
            }
        }

        Console.WriteLine($"Loaded configuration from local file: {apiBaseUrl}");
    }
    catch (Exception fallbackEx)
    {
        Console.WriteLine($"Failed to load local configuration: {fallbackEx.Message}");
        Console.WriteLine($"Using default configuration: {apiBaseUrl}");
    }
}
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Add HttpClient
builder.Services.AddScoped(sp => new HttpClient { BaseAddress = new Uri(builder.HostEnvironment.BaseAddress) });

// Register API base URL as a named service
builder.Services.AddScoped(provider => new ApiConfiguration { BaseUrl = apiBaseUrl });

// Add authentication services
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<CustomAuthenticationStateProvider>();
builder.Services.AddScoped<AuthenticationStateProvider>(provider => provider.GetRequiredService<CustomAuthenticationStateProvider>());

// Add our services
builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();

// Add individual API services
builder.Services.AddScoped<ILessonApiService, LessonApiService>();
builder.Services.AddScoped<ITutorApiService, TutorApiService>();
builder.Services.AddScoped<IStudentApiService, StudentApiService>();
builder.Services.AddScoped<ISubjectApiService, SubjectApiService>();
builder.Services.AddScoped<ILocationApiService, LocationApiService>();
builder.Services.AddScoped<IUserApiService, UserApiService>();

// Add Syncfusion Blazor service
builder.Services.AddSyncfusionBlazor();

await builder.Build().RunAsync();
