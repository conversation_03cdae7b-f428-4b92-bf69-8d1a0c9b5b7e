﻿@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="page">
    <!-- Mobile offcanvas navigation -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobileNavMenu" aria-labelledby="mobileNavMenuLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileNavMenuLabel">
                <i class="fas fa-music me-2"></i>Shining C Music
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <nav class="nav flex-column">
                <AuthorizeView Roles="Administrator">
                    <Authorized>
                        <a class="nav-link" href="/" onclick="closeOffcanvas()">
                            <i class="bi bi-house-door-fill me-2"></i> Home
                        </a>
                        <a class="nav-link" href="/lessons" onclick="closeOffcanvas()">
                            <i class="bi bi-calendar-event me-2"></i> Lessons
                        </a>
                        <a class="nav-link" href="/tutors" onclick="closeOffcanvas()">
                            <i class="bi bi-person-fill me-2"></i> Tutors
                        </a>
                        <a class="nav-link" href="/students" onclick="closeOffcanvas()">
                            <i class="bi bi-people-fill me-2"></i> Students
                        </a>
                        <a class="nav-link" href="/admin" onclick="closeOffcanvas()">
                            <i class="bi bi-gear-fill me-2"></i> Admin
                        </a>
                    </Authorized>
                </AuthorizeView>
                <AuthorizeView Roles="Tutor,Student">
                    <Authorized>
                        <a class="nav-link" href="/lessons" onclick="closeOffcanvas()">
                            <i class="bi bi-calendar-event me-2"></i> My Lessons
                        </a>
                    </Authorized>
                </AuthorizeView>
            </nav>
        </div>
    </div>

    <!-- Desktop sidebar -->
    <div class="sidebar d-none d-lg-block">
        <NavMenu />
    </div>

    <main>
        <!-- Mobile: Show menu button (outside top-row for testing) -->
        <button class="mobile-menu-btn d-lg-none" type="button" id="mobileMenuButton">
            <i class="fas fa-bars"></i>
            <span>MENU</span>
        </button>

        <!-- TEMPORARILY COMMENTED OUT TOP ROW FOR TESTING -->
        <!--
        <div class="top-row px-4">
            <AuthorizeView>
                <Authorized>
                    <div class="user-info">
                        <span class="text-dark me-3 d-none d-md-inline">Welcome, @context.User.Identity?.Name</span>
                        <span class="text-dark me-3 d-md-none">@GetShortName(context.User.Identity?.Name)</span>
                        <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                            <span class="d-none d-md-inline ms-1">Logout</span>
                        </button>
                    </div>
                </Authorized>
            </AuthorizeView>
        </div>
        -->

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

@code {
    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetShortName(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "";

        var parts = fullName.Split(' ');
        if (parts.Length > 1)
            return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

        return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
    }
}
