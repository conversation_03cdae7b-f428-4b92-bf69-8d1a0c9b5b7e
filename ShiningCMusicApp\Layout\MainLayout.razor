﻿@inherits LayoutComponentBase
@using ShiningCMusicApp.Services
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation

<div class="page">
    <!-- Mobile menu toggle button -->
    <button class="mobile-menu-toggle d-lg-none btn btn-primary" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNavMenu" aria-controls="mobileNavMenu">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Mobile offcanvas navigation -->
    <div class="offcanvas offcanvas-start d-lg-none" tabindex="-1" id="mobileNavMenu" aria-labelledby="mobileNavMenuLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileNavMenuLabel">Menu</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body p-0">
            <NavMenu />
        </div>
    </div>

    <!-- Desktop sidebar -->
    <div class="sidebar d-none d-lg-block">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-2 px-md-4 d-flex justify-content-between align-items-center">
            <!-- Mobile: Show menu button and user info -->
            <div class="d-lg-none">
                <button class="btn btn-link p-0 mobile-menu-btn" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNavMenu">
                    <i class="fas fa-bars fs-5"></i>
                </button>
            </div>

            <AuthorizeView>
                <Authorized>
                    <div class="d-flex align-items-center">
                        <span class="text-dark me-2 me-md-3 d-none d-sm-inline">Welcome, @context.User.Identity?.Name</span>
                        <span class="text-dark me-2 d-sm-none">@GetShortName(context.User.Identity?.Name)</span>
                        <button class="btn btn-outline-primary btn-sm" @onclick="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                            <span class="d-none d-md-inline ms-1">Logout</span>
                        </button>
                    </div>
                </Authorized>
            </AuthorizeView>
        </div>

        <article class="content px-2 px-md-4">
            @Body
        </article>
    </main>
</div>

@code {
    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    private string GetShortName(string? fullName)
    {
        if (string.IsNullOrEmpty(fullName))
            return "";

        var parts = fullName.Split(' ');
        if (parts.Length > 1)
            return $"{parts[0][0]}.{parts[^1]}"; // First initial + last name

        return fullName.Length > 10 ? fullName[..10] + "..." : fullName;
    }
}
