﻿using IdentityServer4.Models;
using Microsoft.Extensions.Configuration;

namespace ShiningCMusicApi.Infrastructure
{
    public class AuthConfig
    {
        private readonly IConfiguration _configuration;

        public AuthConfig(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public IEnumerable<ApiResource> ApiResources => new[]
        {
            new ApiResource("ShiningCMusicApi", "Shining C Music API")
            {
                Scopes = { "ShiningCMusicApi" }
            }
        };

        public IEnumerable<ApiScope> ApiScopes => new[]
        {
            new ApiScope("ShiningCMusicApi", "Access to Shining C Music API")
        };
    }
}
