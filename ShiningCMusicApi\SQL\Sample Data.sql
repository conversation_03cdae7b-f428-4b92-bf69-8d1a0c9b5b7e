USE [MusicSchool]
GO

-- Insert sample tutors
INSERT INTO [dbo].[Tu<PERSON>] ([<PERSON><PERSON><PERSON><PERSON>], [<PERSON><PERSON>], [<PERSON><PERSON>Name], [Color])
VALUES 
('<PERSON>', '<EMAIL>', 'sue', '#FF6B6B'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'koeun', '#4ECDC4'),
('<PERSON><PERSON><PERSON>', '<EMAIL>', 'kathrin', '#5742f5'),
('<PERSON>', '<EMAIL>', 'mitchell', '#1e88e5'),
('<PERSON>', '<EMAIL>', 'christy', '#ab47bc')
GO

-- Insert sample students
INSERT INTO [dbo].[Students] ([StudentName], [Email], [SubjectId], [TutorID])
VALUES 
('<PERSON>', '<EMAIL>', 1, 1),
('<PERSON>', '<EMAIL>', 1, 1),
('<PERSON>', '<EMAIL>', 2, 2),
('<PERSON>', '<EMAIL>', 1, 2),
('<PERSON>', '<EMAIL>', 3, 3)
GO

-- Insert sample users
INSERT INTO [dbo].[Users] ([LoginName], [UserName], [Password], [RoleId])
VALUES 
('sue', '<PERSON>', 'password123', 2),
('koeun', 'Koeun', 'password123', 2),
('kathrin', 'Kathrin', 'password123', 2),
('mitchell', 'Mitchell', 'password123', 2),
('christy', 'Christy', 'password123', 1),
('admin', 'Admin', 'admin', 1)
GO
