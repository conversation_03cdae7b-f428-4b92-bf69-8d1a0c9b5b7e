﻿using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicApi.Infrastructure
{
    public class ClientEntity
    {
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public int AccessTokenLifetime { get; set; }
        public List<ClientScopeEntity> AllowedScopes { get; set; } = new List<ClientScopeEntity>();
        public List<ClientGrantTypeEntity> AllowedGrantTypes { get; set; } = new List<ClientGrantTypeEntity>();
    }

    public class ClientScopeEntity
    {
        public int Id { get; set; }
        public string Scope { get; set; }
        public string ClientId { get; set; }
        public ClientEntity Client { get; set; }
    }

    public class ClientGrantTypeEntity
    {
        public int Id { get; set; }
        public string GrantType { get; set; }
        public string ClientId { get; set; }
        public ClientEntity Client { get; set; }
    }
}
