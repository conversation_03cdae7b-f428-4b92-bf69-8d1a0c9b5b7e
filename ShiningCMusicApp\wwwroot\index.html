<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ShiningCMusicApp</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="ShiningCMusicApp.styles.css" rel="stylesheet" />
    <link href="_content/Syncfusion.Blazor.Themes/bootstrap5.css" rel="stylesheet" />
    <script src="_content/Syncfusion.Blazor.Core/scripts/syncfusion-blazor.min.js" type="text/javascript"></script>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global function to close offcanvas
        function closeOffcanvas() {
            const offcanvasElement = document.getElementById('mobileNavMenu');
            if (offcanvasElement) {
                const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
                if (offcanvas) {
                    offcanvas.hide();
                }
            }
        }

        // Initialize offcanvas when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing offcanvas...');

            const offcanvasElement = document.getElementById('mobileNavMenu');
            const menuButton = document.getElementById('mobileMenuButton');

            console.log('Offcanvas element:', offcanvasElement);
            console.log('Menu button:', menuButton);

            if (offcanvasElement && menuButton) {
                // Initialize offcanvas
                const offcanvas = new bootstrap.Offcanvas(offcanvasElement);

                // Add click event to menu button
                menuButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Menu button clicked, showing offcanvas...');
                    offcanvas.show();
                });

                console.log('Offcanvas initialized successfully');
            } else {
                console.error('Failed to find offcanvas element or menu button');
            }
        });
    </script>
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
