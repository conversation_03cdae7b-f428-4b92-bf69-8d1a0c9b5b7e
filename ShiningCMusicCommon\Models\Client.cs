using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class Client
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string ClientId { get; set; } = string.Empty;
        
        [Required]
        [StringLength(2000)]
        public string ClientSecret { get; set; } = string.Empty;
        
        public int AccessTokenLifetime { get; set; }
    }

    public class ClientGrantType
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string ClientId { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string GrantType { get; set; } = string.Empty;
    }

    public class ClientScope
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(200)]
        public string ClientId { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string Scope { get; set; } = string.Empty;
    }
}
