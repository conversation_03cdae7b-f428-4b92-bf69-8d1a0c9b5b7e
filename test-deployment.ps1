# Quick test to check Azure deployment status
param(
    [string]$AppUrl = "https://shiningcmusicapp.azurewebsites.net"
)

Write-Host "Testing Azure deployment..." -ForegroundColor Green

# Test main site
Write-Host "Testing main site: $AppUrl" -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri $AppUrl -Method GET -TimeoutSec 30
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Content Length: $($response.Content.Length)" -ForegroundColor Green
    
    if ($response.Content -like "*blazor*") {
        Write-Host "✓ Blazor content detected" -ForegroundColor Green
    } else {
        Write-Host "✗ No Blazor content found" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error accessing main site: $($_.Exception.Message)" -ForegroundColor Red
}

# Test API
Write-Host "`nTesting API: $AppUrl/api" -ForegroundColor Yellow
try {
    $apiResponse = Invoke-WebRequest -Uri "$AppUrl/api" -Method GET -TimeoutSec 30
    Write-Host "API Status: $($apiResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "API Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test specific API endpoint
Write-Host "`nTesting API endpoint: $AppUrl/api/tutors" -ForegroundColor Yellow
try {
    $tutorsResponse = Invoke-WebRequest -Uri "$AppUrl/api/tutors" -Method GET -TimeoutSec 30
    Write-Host "Tutors API Status: $($tutorsResponse.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "Tutors API Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed." -ForegroundColor Green
