using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class LocationsController : ControllerBase
    {
        private readonly ILocationService _locationService;
        private readonly ILogger<LocationsController> _logger;

        public LocationsController(ILocationService locationService, ILogger<LocationsController> logger)
        {
            _locationService = locationService;
            _logger = logger;
        }

        // GET: api/locations
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Location>>> GetLocations()
        {
            try
            {
                var locations = await _locationService.GetLocationsAsync();
                return Ok(locations);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving locations");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/locations/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Location>> GetLocation(int id)
        {
            try
            {
                var location = await _locationService.GetLocationAsync(id);
                if (location == null)
                {
                    return NotFound(new { message = "Location not found" });
                }
                return Ok(location);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving location");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/locations
        [HttpPost]
        public async Task<ActionResult<Location>> CreateLocation([FromBody] Location location)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(location.LocationName))
                {
                    return BadRequest(new { message = "Location name is required" });
                }

                var createdLocation = await _locationService.CreateLocationAsync(location);
                return CreatedAtAction(nameof(GetLocation), new { id = createdLocation.LocationId }, createdLocation);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating location");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/locations/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateLocation(int id, [FromBody] Location location)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(location.LocationName))
                {
                    return BadRequest(new { message = "Location name is required" });
                }

                var success = await _locationService.UpdateLocationAsync(id, location);
                if (success)
                {
                    return Ok(new { message = "Location updated successfully" });
                }
                return NotFound(new { message = "Location not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating location");
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/locations/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteLocation(int id)
        {
            try
            {
                var success = await _locationService.DeleteLocationAsync(id);
                if (success)
                {
                    return Ok(new { message = "Location deleted successfully" });
                }
                return NotFound(new { message = "Location not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting location");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
