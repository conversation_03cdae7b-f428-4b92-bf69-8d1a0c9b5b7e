using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using System.Security.Claims;
using System.Text.Json;

namespace ShiningCMusicApp.Services
{
    public class CustomAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly IUserApiService _userApi;
        private readonly string USER_KEY = "currentUser";

        public CustomAuthenticationStateProvider(IJSRuntime jsRuntime, IUserApiService userApi)
        {
            _jsRuntime = jsRuntime;
            _userApi = userApi;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                var userJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", USER_KEY);
                
                if (string.IsNullOrEmpty(userJson))
                {
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var user = JsonSerializer.Deserialize<User>(userJson);
                if (user == null)
                {
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, user.UserName ?? user.LoginName),
                    new Claim(ClaimTypes.NameIdentifier, user.LoginName),
                    new Claim("UserId", user.LoginName),
                    new Claim("RoleId", user.RoleId?.ToString() ?? "0")
                };

                // Add role claims
                switch (user.RoleId)
                {
                    case 1:
                        claims.Add(new Claim(ClaimTypes.Role, "Administrator"));
                        break;
                    case 2:
                        claims.Add(new Claim(ClaimTypes.Role, "Tutor"));
                        break;
                    case 3:
                        claims.Add(new Claim(ClaimTypes.Role, "Student"));
                        break;
                }

                var identity = new ClaimsIdentity(claims, "custom");
                return new AuthenticationState(new ClaimsPrincipal(identity));
            }
            catch
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        public async Task<bool> LoginAsync(string loginName, string password)
        {
            try
            {
                Console.WriteLine($"CustomAuthenticationStateProvider.LoginAsync called with: {loginName}");

                var user = await _userApi.AuthenticateAsync(loginName, password);
                Console.WriteLine($"Authentication result: {user?.LoginName} - RoleId: {user?.RoleId}");

                if (user != null)
                {
                    var userJson = JsonSerializer.Serialize(user);
                    Console.WriteLine($"Storing user in localStorage: {userJson}");

                    await _jsRuntime.InvokeVoidAsync("localStorage.setItem", USER_KEY, userJson);

                    NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
                    Console.WriteLine("Authentication state changed notification sent");
                    return true;
                }
                Console.WriteLine("Authentication failed - user is null");
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"LoginAsync error: {ex.Message}");
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", USER_KEY);
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            try
            {
                var userJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", USER_KEY);
                if (string.IsNullOrEmpty(userJson))
                    return null;
                
                return JsonSerializer.Deserialize<User>(userJson);
            }
            catch
            {
                return null;
            }
        }
    }
}
