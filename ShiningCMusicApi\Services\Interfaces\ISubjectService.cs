using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface ISubjectService
    {
        Task<IEnumerable<Subject>> GetSubjectsAsync();
        Task<Subject?> GetSubjectAsync(int id);
        Task<Subject> CreateSubjectAsync(Subject subject);
        Task<bool> UpdateSubjectAsync(int id, Subject subject);
        Task<bool> DeleteSubjectAsync(int id);
    }
}
