{
  "ConnectionStrings": {
    "MusicSchool": "Server=tcp:shining.database.windows.net,1433;Initial Catalog=MusicSchool;Persist Security Info=False;User ID=sam;Password=********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  },
  "ApiBaseUrl": "https://localhost:7268/",
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*"
}

  //"ConnectionStrings": {
  //  "MusicSchool": "data source=localhost;initial catalog=MusicSchool;persist security info=True;user id=sa;password=**********;MultipleActiveResultSets=True;Encrypt=false;"
  //},
  //"ApiBaseUrl": "https://localhost:7268/",