﻿@using ShiningCMusicApp.Services
@inject NavigationManager Navigation
@inject CustomAuthenticationStateProvider AuthStateProvider


<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">
            <i class="fas fa-music me-2"></i>
            <span class="d-none d-sm-inline">Shining C Music</span>
            <span class="d-sm-none">SCM</span>
        </a>
        <button title="Navigation menu" class="navbar-toggler d-lg-none" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable"
    <nav class="flex-column">
        <AuthorizeView Roles="Administrator">
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="" Match="NavLinkMatch.All" data-bs-dismiss="offcanvas">
                        <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">Home</span>
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="lessons" data-bs-dismiss="offcanvas">
                        <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">Lessons</span>
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="tutors" data-bs-dismiss="offcanvas">
                        <span class="bi bi-person-fill-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">Tutors</span>
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="students" data-bs-dismiss="offcanvas">
                        <span class="bi bi-people-fill-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">Students</span>
                    </NavLink>
                </div>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="admin" data-bs-dismiss="offcanvas">
                        <span class="bi bi-gear-fill-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">Admin</span>
                    </NavLink>
                </div>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Roles="Tutor,Student">
            <Authorized>
                <div class="nav-item px-3">
                    <NavLink class="nav-link" href="lessons" data-bs-dismiss="offcanvas">
                        <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span>
                        <span class="nav-text">My Lessons</span>
                    </NavLink>
                </div>
            </Authorized>
        </AuthorizeView>

@*         <div class="nav-item px-3">
            <NavLink class="nav-link" href="scheduler">
                <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Scheduler
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="weather">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> Weather
            </NavLink>
        </div> *@
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;
    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }
}
