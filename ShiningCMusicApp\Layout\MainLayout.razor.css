.page {
    position: relative;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

/* Mobile styles */
.mobile-menu-toggle {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1050;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-menu-btn {
    color: #333;
    text-decoration: none;
}

.mobile-menu-btn:hover {
    color: #007bff;
}

@media (max-width: 991.98px) {
    .page {
        flex-direction: column;
    }

    .top-row {
        justify-content: space-between;
        padding-left: 60px !important; /* Make room for mobile menu button */
        height: 3rem;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }

    .content {
        padding-top: 1rem;
    }

    /* Offcanvas customization */
    .offcanvas {
        width: 280px;
    }

    .offcanvas-body {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    }
}

@media (min-width: 992px) {
    .page {
        flex-direction: row;
    }

    .sidebar {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1;
        padding-left: 2rem !important;
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .content {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    /* Hide mobile menu button on desktop */
    .mobile-menu-toggle {
        display: none;
    }
}
