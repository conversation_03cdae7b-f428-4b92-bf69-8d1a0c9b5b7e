.page {
    position: relative;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

.sidebar {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row ::deep a, .top-row ::deep .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

    .top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
        text-decoration: underline;
    }

    .top-row ::deep a:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
    }

/* Mobile styles */
.mobile-menu-btn {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #333 !important;
    text-decoration: none !important;
    border: none !important;
    background: none !important;
    font-size: 1.5rem !important;
    padding: 0.5rem !important;
    z-index: 1000;
    display: block !important;
}

.mobile-menu-btn:hover {
    color: #007bff !important;
    text-decoration: none !important;
}

.mobile-menu-btn:focus {
    box-shadow: none !important;
    outline: none !important;
    text-decoration: none !important;
}

.user-info {
    margin-left: auto;
    display: flex;
    align-items: center;
}

@media (max-width: 991.98px) {
    .page {
        flex-direction: column;
    }

    .top-row {
        position: relative;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        height: 3.5rem;
        padding-left: 4rem !important;
        padding-right: 1rem !important;
    }

    .top-row ::deep a, .top-row ::deep .btn-link {
        margin-left: 0;
    }

    .content {
        padding-top: 1rem;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    .mobile-menu-btn {
        display: block !important;
    }

    /* Offcanvas customization */
    .offcanvas {
        width: 280px;
    }

    .offcanvas-body {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        padding: 0;
    }

    .offcanvas-header {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        color: white;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .offcanvas-title {
        color: white;
        font-size: 1.1rem;
    }

    .btn-close {
        filter: invert(1);
    }
}

@media (min-width: 992px) {
    .page {
        flex-direction: row;
    }

    .sidebar {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }

    /* Hide mobile menu button on desktop */
    .mobile-menu-btn {
        display: none !important;
    }
}
